{% extends 'base.html.twig' %}

{% block body %}
    <form method="get">
        <fieldset>
            <legend>{{ msg('form-heading') }}</legend>
            <div class="form-group">
                <label for="image">{{ msg('image-url') }}</label>
                <input name="image" id="image" class="form-control" type="url" value="{{ image }}" required />
                <p class="help-block">{{ msg( 'image-url-help', [image_hosts] ) }}</p>
            </div>
            <div class="form-group">
                <label>{{ msg('engine') }}</label>
                <div class="radio">
                    <label>
                        <input type="radio" name="engine" value="google" {% if engine == 'google' %}checked{% endif %} />
                        {{ msg('engine-name-google') }}
                    </label>
                </div>
                <div class="radio">
                    <label>
                        <input type="radio" name="engine" value="tesseract" {% if engine == 'tesseract' %}checked{% endif %} />
                        {{ msg('engine-name-tesseract') }}
                    </label>
                </div>
                <div class="radio">
                    <label>
                        <input type="radio" name="engine" value="transkribus" {% if engine == 'transkribus' %}checked{% endif %} />
                        {{ msg('engine-name-transkribus') }}
                    </label>
                </div>
            </div>
            <div class="form-group">
                <label id="optional-lang-label" for="lang" class="lang-label {% if engine == 'transkribus' %}hidden{% endif %}">
                    {{ msg('language-code') }}
                </label>
                <label id="transkribus-lang-label" for="lang" class="lang-label {% if engine != 'transkribus' %}hidden{% endif %}">
                    {{ msg('transkribus-language-code') }}
                </label>
                <select 
                    id="lang" 
                    class="form-control" 
                    name="langs[]" 
                    multiple 
                    {% if engine == 'transkribus' %}
                        required
                    {% else %}
                        data-placeholder="{{ msg('langs-placeholder') }}"
                    {% endif %}
                >
                    {% for lang_code,lang_name in available_langs %}
                        <option value="{{ lang_code }}" {% if lang_code in langs %}selected{% endif %}>
                            {{- lang_code }} &ndash; {{ lang_name -}}
                        </option>
                    {% endfor %}
                </select>
                {% include '_transkribus_help.html.twig' with {engine: engine} %}
            </div>
        </fieldset>

        {% include '_tesseract_options.html.twig' with {engine: engine} %}

        {% include '_transkribus_options.html.twig' with {engine: engine} %}

        <div class="form-group submit-btn">
            <input type="submit" value="{{ msg( 'submit' ) }}" class="btn btn-info" />
        </div>

        <div class="loader hidden">
            <p>
                <span class="glyphicon glyphicon-refresh" aria-hidden="true"></span>
                {{ msg('loading-message') }}
            </p>
        </div>

        {% if text is defined %}
            <div class="ocr-output">
                <hr/>
                <p class="nojs-hide">{{ msg('drag-help') }}</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="output-buttons nojs-hide">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-default drag-mode drag-mode-crop"
                                        title="{{ msg('drag-mode-crop') }}" data-drag-mode="crop">
                                    <img src="{{ asset('build/images/Crop_-_The_Noun_Project.svg') }}" height="20" width="20"
                                         alt="{{ msg('drag-mode-crop-alt') }}" />
                                </button>
                                <button type="button" class="btn btn-default drag-mode drag-mode-move active"
                                        title="{{ msg('drag-mode-move') }}" data-drag-mode="move">
                                    <img src="{{ asset('build/images/OOjs_UI_icon_move.svg') }}" height="20" width="20"
                                         alt="{{ msg('drag-mode-move-alt') }}" />
                                </button>
                            </div>

                            <input type="submit" value="{{ msg( 'submit-crop' ) }}" class="submit-crop btn btn-info disabled" disabled />

                            <input type="hidden" name="crop[x]" value="{% if crop.x is defined %}{{ crop.x }}{% endif %}" />
                            <input type="hidden" name="crop[y]" value="{% if crop.y is defined %}{{ crop.y }}{% endif %}" />
                            <input type="hidden" name="crop[width]" value="{% if crop.width is defined %}{{ crop.width }}{% endif %}" />
                            <input type="hidden" name="crop[height]" value="{% if crop.height is defined %}{{ crop.height }}{% endif %}" />
                        </div>
                        <div><!-- This div is required for Cropper.js -->
                            <img id="source-image" class="img-responsive" src="{{ app.request.get('image') }}" alt="{{ msg('img-alt-text') }}" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="output-buttons nojs-hide">
                            <button class="btn btn-info copy-button" data-copied-text="{{ msg('copied-to-clipboard') }}">{{ msg( 'copy-to-clipboard' ) }}</button>
                        </div>
                        <textarea class="form-control" rows="{{ text|textarea_rows }}" id="text">{{ text }}</textarea>
                    </div>
                </div>
            </div>
        {% endif %}
    </form>

{% endblock %}
