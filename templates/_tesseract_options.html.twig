<fieldset id="tesseract-options" class="engine-options {% if engine != 'tesseract' %}hidden{% endif %}">
    <legend>{{ msg('tesseract-options') }}</legend>
    <div class="form-group">
        <label for="psm">{{ msg('tesseract-psm-label') }}</label>
        <select name="psm" id="psm" class="form-control">
            {% for psm_info in available_psms %}
                <option value="{{ psm_info.value }}" {% if psm_info.value == psm %}selected="selected"{% endif %}>{{ psm_info.label }}</option>
            {% endfor %}
        </select>
        <p class="help-block">
            {{ msg('tesseract-psm-help') }}
        </p>
    </div>
</fieldset>
