<!DOCTYPE html>
<html lang="{{ lang() }}" class="nojs">
    <head>
        <meta charset="UTF-8">
        <title>{% block title %}{{ msg('title') }}{% endblock %}</title>
        <meta property="og:title" content="{{ msg('title') }}" />
        <meta property="og:description" content="{{ msg('subtitle') }}"/>
        <meta property="og:type" content="website" />
        <meta property="og:url" content="{{ url('home') }}"/>
        <meta property="og:image" content="{{ absolute_url(asset('build/images/WikimediaOCR-logo.svg')) }}" />
        <meta property="og:image:width" content="50" />
        <meta property="og:image:height" content="50" />
        <meta property="og:image:type" content="image/svg+xml" />
        <meta property="og:image:alt" content="WikimediaOCR Logo" />
        <link rel="icon" href="{{ asset('favicon.ico') }}" />
        {% block stylesheets %}
            {{ encore_entry_link_tags('app') }}
            {% if is_rtl() %}
                <link type="text/css" href="https://tools-static.wmflabs.org/cdnjs/ajax/libs/bootstrap-rtl/3.4.0/css/bootstrap-flipped.css" rel="stylesheet"/>
            {% endif %}
        {% endblock %}
        {% block javascripts %}
            {{ encore_entry_script_tags('app') }}
        {% endblock %}
    </head>
    <body class="{% if is_rtl() %}rtl{% endif %}">
        <header class="page-header">
            <div class="container">
                <p class="logo">
                    <img src="{{ asset('build/images/WikimediaOCR-logo.svg') }}" alt="{{ msg('title') }}" />
                </p>
                <div>
                    <h1 class="page-title">{{ msg('title') }}</h1>
                    <p class="page-subtitle">{{ msg('subtitle') }}</p>
                </div>
            </div>
        </header>
        <main class="container">
            {% for label, messages in app.flashes(['error']) %}
                {% for message in messages %}
                    <div class="alert alert-danger">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endfor %}
            {% for label, messages in app.flashes(['warning']) %}
                {% for message in messages %}
                    <div class="alert alert-warning">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endfor %}

            {% block body %}{% endblock %}

            <hr/>
            <footer>
                <a target="_blank" href="https://github.com/wikimedia/wikimedia-ocr/releases/tag/{{ git_tag() }}">
                    {{ msg('version', [git_tag()]) -}}
                </a>
                &bull;
                <a target="_blank" href="https://www.mediawiki.org/wiki/Help:Extension:Wikisource/Wikimedia_OCR">
                    {{ msg('documentation') -}}
                </a>
                &bull;
                <a href="{{ path('app.swagger_ui') }}" title="{{ msg('api-tooltip') }}">
                    {{ msg('api') -}}
                </a>
                &bull;
                <a target="_blank" href="https://phabricator.wikimedia.org/maniphest/task/edit/form/1/?tags=wikimedia_ocr">
                    {{ msg('report-issue') -}}
                </a>
            </footer>
        </main>
    </body>
</html>
