{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> p"]}, "title": "App title", "subtitle": "Show below the application title.", "form-heading": "Heading for the main form.", "image-url": "Form label for the 'image' field.", "image-url-help": "Help text for the 'image' field.", "image-url-error": "Error message shown when the 'image' field is not valid.", "image-alt-text": "Alt text for the displayed image, next to the OCR text box.", "language-code": "Form label for the 'languages' field.", "engine": "Form label for the 'engine' field.\nOCR is a common abbreviation in English for \"Optical Characters Recognition\".", "engine-name-google": "Option text for the 'google' engine.\nOCR is a common abbreviation in English for \"Optical Characters Recognition\".", "engine-name-tesseract": "Option text for the 'tesseract' engine.", "engine-name-transkribus": "Option text for the 'transkribus' engine.\nOCR is a common abbreviation in English for \"Optical Characters Recognition\".", "engine-not-found-warning": "Error message displayed when an invalid engine is requested.\n\nParameter:\n* $1 – the requested engine.\n* $2 – the default engine that is being used instead.", "engine-invalid-langs-warning": "Warning message displayed when invalid or unsupported languages are requested.\n\nParameter:\n* $1 – a comma-separated list of invalid languages.", "submit": "Submit button text for when the whole image is being submitted.", "submit-crop": "Submit button text for when the image is being cropped.", "drag-help": "Instructional sentence above the image and cropping buttons.", "drag-mode-move": "Tooltip text for the 'move' mode radio button.", "drag-mode-move-alt": "Alt text for the 'move' icon.", "drag-mode-crop": "Tooltip text for the 'crop' mode radio button.", "drag-mode-crop-alt": "Alt text for the 'crop' icon.", "copy-to-clipboard": "Button text for the copy-to-clipboard button.", "copied-to-clipboard": "Button text shown after the copy-to-clipboard button is clicked.", "google-error": "Error message prefix for the Google engine.\n\nParameter:\n* $1 – the error message.", "image-retrieval-failed": "Error message displayed when the image could not be found.\n\nParameter:\n* $1 – the error message.", "documentation": "Link text in the footer for the documentation link\n{{identical|Documentation}}", "api": "Link text for the API link in the footer.\n\n{{Optional}}", "api-tooltip": "Tooltip for the API link in the footer.", "version": "Link text in the footer for the source code link.\n\nParameter:\n* $1 – the current application version number.\n\n{{identical|Version}}", "report-issue": "Link text in the footer for the issue-reporting link.", "langs-placeholder": "Placeholder text for the language input field.", "langs-param-error": "Error message displayed when invalid language(s) are submitted.\n\nParameters:\n* $1 – number of invalid languages\n* $2 - the list of invalid languages\n\nOCR is a common abbreviation in English for \"Optical Characters Recognition\".", "loading-message": "Loading message displayed when the transcription is being performed", "tesseract-options": "Heading for Tesseract-specific options.", "tesseract-psm-label": "Form label for the Tesseract page segmentation mode.", "tesseract-psm-help": "Help text for the Tesseract page segmentation mode option. 'Sparse text' refers to options, see messages:\n* {{msg-wm|Wikimedia-ocr-tesseract-psm-11}} and\n* {{msg-wm|Wikimedia-ocr-tesseract-psm-12}}.", "tesseract-psm-0": "Form option for Tesseract page segmentation mode.", "tesseract-psm-1": "Form option for Tesseract page segmentation mode.", "tesseract-psm-2": "Form option for Tesseract page segmentation mode.\n\nOCR is a common abbreviation in English for \"Optical Characters Recognition\". OSD is a common abbreviation in English for \"Orientation and Script Detection\", i.e. the processing steps needed after segmentation of lines of text, that allow runs of recognized individual glyphs to be reordered correctly in logical order when they are converted and encoded into plain text characters.\n\nThis option just performs basic segmentation of the page; it uses generic statistical analysis of images to detect margins, interlines and whitespaces found inside captured images of texts that use coherent glyph sizes and styles, in order to return a set of rectangular blocks of text and to heuristically detect their rotation; it separates images and decorations that are not likely to be text because they are not enough contrasted or just appear to be noisy points.", "tesseract-psm-3": "Form option for Tesseract page segmentation mode.", "tesseract-psm-4": "Form option for Tesseract page segmentation mode.", "tesseract-psm-5": "Form option for Tesseract page segmentation mode.", "tesseract-psm-6": "Form option for Tesseract page segmentation mode.", "tesseract-psm-7": "Form option for Tesseract page segmentation mode.", "tesseract-psm-8": "Form option for Tesseract page segmentation mode.", "tesseract-psm-9": "Form option for Tesseract page segmentation mode.", "tesseract-psm-10": "Form option for Tesseract page segmentation mode.", "tesseract-psm-11": "Form option for Tesseract page segmentation mode.", "tesseract-psm-12": "Form option for Tesseract page segmentation mode.", "tesseract-psm-13": "Form option for Tesseract page segmentation mode.", "tesseract-param-error": "Error message displayed when invalid values for Tesseract options are submitted.\n\nParameters:\n* $1 – the form label for the option. Currently, the only possible value here is {{msg-wm|wikimedia-ocr-tesseract-psm-label}}.\n* $2 – The value that was given.\n* $3 – the maximum value for the option (this will be an integer).", "tesseract-no-text-error": "Error message displayed when Tesseract returned no text (e.g. for an image that has no text in it).", "tesseract-internal-error": "Generic error message displayed when the tesseract command fails.", "transkribus-language-code": "Form label for the 'Language Model' field for the Transkribus engine", "transkribus-unauthorized-error": "Error message displayed when Transkribus access token has expired or login credentials are invalid.", "transkribus-default-error": "Generic error message displayed when Transkribus API returns error status different from 401.", "transkribus-empty-response-error": "Error message displayed when Transkribus API returns an empty response body.", "transkribus-init-process-error": "Error message displayed when Transkribus API fails to initialize upload process.", "transkribus-failed-process-error": "Error message displayed when Transkribus API process status returns FAILED.", "transkribus-no-lang-error": "Error message displayed when no language is selected from the Languages drop down menu", "transkribus-multiple-lang-error": "Error message displayed when multiple languages are chosen from the Languages drop down menu", "transkribus-browse-public-models": "Link text for the list of all public models for Transkribus", "transkribus-request-for-model": "Link text for the form to make a request for a model to be added to the OCR tool", "transkribus-options": "Heading for Transkribus-specific options", "transkribus-line-label": "Form label for the Transkribus line detection model selection.", "transkribus-line-id-none-option": "Form option for Transkribus line detection model", "transkribus-mixed-line-option": "Form option for Transkribus line detection model", "transkribus-line-help": "Help text for the Transkribus line detection model selection.", "transkribus-jobs": "Header text for the Transkribus jobs' page.", "transkribus-job-id": "Table column header text for the job ID.", "transkribus-job-state": "Table column header text (state is 'started', 'completed', etc.).", "transkribus-job-description": "Table column header text for the job description.", "transkribus-job-start": "Table column header text for the job-started date.", "transkribus-job-end": "Table column header text for the job-ended date", "transkribus-job-waited": "Table column header text for the job start-delay, in minutes."}