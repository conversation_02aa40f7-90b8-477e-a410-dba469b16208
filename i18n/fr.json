{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "Derugon", "<PERSON><PERSON><PERSON>", "Hecatonchire", "JenyxGym", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> p", "Wladek92"]}, "title": "Reconnaissance optique des caractères (ROC) de Wikimedia", "subtitle": "Un outil pour transcrire du texte à partir d’images numérisées sur Wikimédia Commons, à destination de Wikisource ou autre.", "form-heading": "Transcrire une image", "image-url": "URL de l’image", "image-url-help": "Insérer l’URL d’une image hébergée sur un serveur Wikimédia comme : $1", "image-url-error": "L’URL de l’image doit commencer par {{PLURAL:$1|le nom de domaine suivant|un des noms de domaine suivants}} et se terminer par une extension de fichier valide : $2", "image-alt-text": "L’image d’origine", "language-code": "<PERSON><PERSON> (facultatif)", "engine": "Moteur ROC", "engine-name-google": "ROC de Google Cloud Vision", "engine-name-tesseract": "ROC de Tesseract", "engine-name-transkribus": "ROC de Transkribus", "engine-not-found-warning": "Le moteur « $1 » demandé n’a pas été trouvé. Utilisation du moteur par défaut « $2 » à la place.", "engine-invalid-langs-warning": "Les langues suivantes ne sont pas valides ou ne sont pas prises en charge par le moteur et ont été ignorées : $1", "submit": "Transcrire toute la page", "submit-crop": "Transcrire la zone", "drag-help": "Sélectionner l’outil de découpe et tirer un rectangle sur l’image ci-dessous pour transcrire seulement une zone de la page.", "drag-mode-move": "Trainer d<PERSON><PERSON><PERSON> l’image", "drag-mode-move-alt": "Icône représentant l’action « déplacer ».", "drag-mode-crop": "Trainer créera une nouvelle zone de découpe", "drag-mode-crop-alt": "Icône représentant l’action « découper ».", "copy-to-clipboard": "Copier dans le presse-papiers", "copied-to-clipboard": "Copié !", "google-error": "Le service Google a renvoyé une erreur : $1", "image-retrieval-failed": "Échec de récupération de l’image : $1", "documentation": "Documentation", "api": "API", "api-tooltip": "Consulter la documentation de l’API", "version": "Version $1", "report-issue": "Signaler un problème", "langs-placeholder": "Laissez vide pour la détection automatique de la langue.", "langs-param-error": "L{{PLURAL:$1|a|es}} langue{{PLURAL:$1||s}} suivante{{PLURAL:$1||s}} {{PLURAL:$1|n’est|ne sont}} plus prise{{PLURAL:$1||s}} en charge par le moteur ROC : $2", "loading-message": "Transcription en cours...", "tesseract-options": "Options de Tesseract", "tesseract-psm-label": "Méthode de segmentation de page", "tesseract-psm-help": "Essayer « Texte épars » pour une meilleure prise en charge du multicolonnage.", "tesseract-psm-0": "Détection de l’orientation et de l’écriture (OSD) uniquement.", "tesseract-psm-1": "Segmentation de page automatique avec OSD.", "tesseract-psm-2": "Segmentation de page automatique, mais sans détection de l’écriture et de l’orientation (OSD), ni reconnaissance optique des caractères (ROC) : mises en œuvre indisponibles.", "tesseract-psm-3": "Segmentation de page entièrement automatique, mais sans OSD (par défaut).", "tesseract-psm-4": "Supposer une unique colonne de texte de taille variable.", "tesseract-psm-5": "Supposer un unique bloc uniforme de texte aligné verticalement.", "tesseract-psm-6": "Supposer un bloc unique uniforme de texte.", "tesseract-psm-7": "Traiter l’image comme une unique ligne de texte.", "tesseract-psm-8": "Traiter l’image comme un mot unique.", "tesseract-psm-9": "Traiter l’image comme un mot unique dans un cercle.", "tesseract-psm-10": "Traiter l’image comme un caractère unique.", "tesseract-psm-11": "Texte épars. Trouver autant de texte que possible, sans ordre particulier.", "tesseract-psm-12": "Texte épars avec OSD.", "tesseract-psm-13": "Ligne brute. Traiter l’image comme une ligne de texte unique, en sautant les raccourcis spécifiques à Tesseract.", "tesseract-param-error": "L’option « $1 » avec une valeur de $2 n’est pas prise en charge par Tesseract. Valeur maximale : $3", "tesseract-no-text-error": "Le moteur Tesseract n'a renvoyé aucun texte pour cette image.", "tesseract-internal-error": "Le moteur Tesseract a renvoyé une erreur interne.", "transkribus-language-code": "<PERSON><PERSON><PERSON><PERSON>", "transkribus-unauthorized-error": "Code d’erreur « $1 » : la requête n’est pas autorisée", "transkribus-default-error": "Code d’erreur « $1 » : impossible d’effectuer la requête, ve<PERSON><PERSON><PERSON> réessayer !", "transkribus-empty-response-error": "Impossible d’analyser le résultat de l’API Transkribus", "transkribus-init-process-error": "Échec d’initialisation du processus Transkribus", "transkribus-failed-process-error": "Le processus Transkribus a échoué", "transkribus-no-lang-error": "Aucune langue n’a été sélectionnée", "transkribus-multiple-lang-error": "Plusieurs langues ne sont pas autorisées, spécifiez une seule langue.", "transkribus-browse-public-models": "Parcourir tous les modèles publics de langue pour Transkribus", "transkribus-request-for-model": "Faire une demande pour ajouter un modèle de Transkribus à l’outil OCR", "transkribus-options": "Options de Transkribus", "transkribus-line-label": "Mod<PERSON>le de détection de ligne", "transkribus-line-id-none-option": "Aucun", "transkribus-mixed-line-option": "Orientation de ligne mixte", "transkribus-line-help": "Laisser vide si vous n’êtes pas sûr de quel modèle de détection de ligne utiliser", "transkribus-jobs": "Tâches de Transkribus", "transkribus-job-id": "Identifiant de la tâche", "transkribus-job-state": "État", "transkribus-job-description": "Description", "transkribus-job-start": "<PERSON><PERSON><PERSON><PERSON>", "transkribus-job-end": "<PERSON><PERSON><PERSON><PERSON> le", "transkribus-job-waited": "<PERSON><PERSON><PERSON> (en minutes)"}