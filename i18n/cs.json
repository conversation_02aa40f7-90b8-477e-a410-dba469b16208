{"@metadata": {"authors": ["Georg101"]}, "title": "Wikimédia OCR", "subtitle": "Přepis textu z obrázků", "form-heading": "Přepsat obrázek", "image-url": "Adresa URL obrázku", "image-url-help": "Vložte adresu URL obrázku na server Wikimedia, například: $1", "image-url-error": "URL obrázku musí začínat na {{PLURAL:$1| nasledující název domény | jeden z následujících názvů domén}} a končit platnou príponou souboru: $2", "image-alt-text": "Původní obrázek", "language-code": "Jazyky (volitelné)", "engine": "engine OCR", "engine-not-found-warning": "Požadovaný modul „ $1 “ nebyl nalezen. Místo toho se používá výchozí modul „ $2", "engine-invalid-langs-warning": "Následující jazyky jsou neplatné nebo nepodporované a byly ignorované: $1", "submit": "Přepsat", "copy-to-clipboard": "Kopírovat do schránky", "copied-to-clipboard": "Zkopírováno!", "google-error": "Služba Google vrátila chybu: $1", "image-retrieval-failed": "Načítání obrázku selhalo: $1", "documentation": "Dokumentace", "version": "Verze $1", "report-issue": "Nahlásit problém", "langs-placeholder": "Pro automatickou detekci jazyka nechejte pole prázdné.", "langs-param-error": "{{PLURAL:$1|Nasledující jazyk není je podporovaný|Nasledující jazyky nejsou podporované}} modulem OCR: $2", "tesseract-options": "Možnosti Tesseractu", "tesseract-psm-label": "Metoda segmentace s<PERSON>", "tesseract-psm-help": "Vyzkoušejte „Řídký text“ pro lepší podporu vícero sloupců.", "tesseract-psm-0": "Jenom orientace a detekce skriptu (OSD).", "tesseract-psm-1": "Automatická segmentace stránek pomocí OSD.", "tesseract-psm-2": "Automatická segmentace s<PERSON>, ale bez OSD nebo OCR. (nen<PERSON>)", "tesseract-psm-3": "Plně automatická segment<PERSON> s<PERSON>, ale bez OSD. (Výchozí)", "tesseract-psm-4": "Předpokl<PERSON>dan<PERSON> jeden sloupec textu s proměnlivou velikostí.", "tesseract-psm-5": "Předpokládaný jeden jednotný blok vertikálně zarovnaného textu.", "tesseract-psm-6": "Předpokládaný jeden jednotný blok textu.", "tesseract-psm-7": "Zach<PERSON>zet s obrázkem jako s jedním řádkem textu.", "tesseract-psm-8": "Zach<PERSON><PERSON><PERSON> s obrázkem jako s jedním slovem.", "tesseract-psm-9": "Zacházet s obrázkem jako s jedním slovem v kruhu.", "tesseract-psm-10": "Zacházet s obrázkem jako s jedním znakem.", "tesseract-psm-11": "Řídký text. Najděte co nejvíce textu v rozházeném pořadí.", "tesseract-psm-12": "Řídký text s OSD.", "tesseract-psm-13": "Syrový řádek. Zach<PERSON><PERSON>t s obrázkem jako s jedním řádkem textu a obch<PERSON>zet hacky, které jsou specifické pro Tesseract.", "tesseract-param-error": "Tesseract nepodporuje možnost ' $1 ' s hodnotou $2. Maximální hodnota: $3", "tesseract-internal-error": "Engine tesseractu vrátil interní chybu."}