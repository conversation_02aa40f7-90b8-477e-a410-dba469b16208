{"@metadata": {"authors": ["Apzp79", "Namoroka", "Suleiman the Magnificent Television", "<PERSON><PERSON><PERSON><PERSON>", "그냥기여자"]}, "title": "위키미디어OCR", "subtitle": "위키미디어 공용의 스캔된 이미지에서 텍스트를 변환하여 위키문헌과 다른 곳에서 사용할 수 있는 도구입니다.", "form-heading": "그림을 변환하기", "image-url": "그림 URL", "image-url-help": "위키미디어 서버에 호스팅된 그림 URL을 삽입하여 다음을 입력합니다: $1", "image-url-error": "그림 URL은 {{PLURAL:$1|다음 도메인 이름으로|다음 도메인 이름 중 하나로}} 시작하여 유효한 파일 확장자로 끝나야 합니다: $2", "image-alt-text": "원본 그림", "language-code": "언어 (선택 사항)", "engine": "OCR 엔진", "engine-name-google": "구글 클라우드 비전 OCR", "engine-name-tesseract": "테서랙트 OCR", "engine-name-transkribus": "트랜스크리버스 OCR", "engine-not-found-warning": "요청하신 '$1' 엔진을 찾을 수 없습니다. 대신 기본 엔진 '$2'을 사용합니다.", "engine-invalid-langs-warning": "다음 언어는 잘못되었거나 엔진에서 지원하지 않으므로 무시되었습니다: $1", "submit": "전체 페이지 변환", "submit-crop": "변환 영역", "drag-help": "자르기 도구를 선택하고 사각형을 아래의 그림 위로 드래그하여 페이지의 일부 영역만 변환합니다.", "drag-mode-move": "드래그하면 그림이 이동합니다", "drag-mode-move-alt": "'이동' 동작을 나타내는 아이콘입니다.", "drag-mode-crop": "드래그하면 새로운 자르기 영역이 생성됩니다", "drag-mode-crop-alt": "'자르기' 동작을 나타내는 아이콘입니다.", "copy-to-clipboard": "클립보드에 복사", "copied-to-clipboard": "복사 완료!", "google-error": "구글 서비스가 오류를 반환했습니다: $1", "image-retrieval-failed": "그림 검색에 실패하였습니다: $1", "documentation": "문서", "api-tooltip": "API 문서 보기", "version": "버전 $1", "report-issue": "문제점 보고", "langs-placeholder": "자동 언어 감지를 위해 비워두세요.", "langs-param-error": "다음 {{PLURAL:$1|언어}}는 OCR 엔진에서 지원되지 않습니다: $2", "loading-message": "변환 수행 중...", "tesseract-options": "테서랙트 옵션", "tesseract-psm-label": "페이지 분할 방법", "tesseract-psm-help": "더 나은 다단 지원을 위해 \"산발적인 텍스트\"를 사용해 보세요.", "tesseract-psm-0": "방향 및 문자 인식(OSD)만 수행합니다.", "tesseract-psm-1": "OSD를 이용하여 페이지를 자동 분할합니다.", "tesseract-psm-2": "OSD나 OCR 없이 페이지를 자동 분할합니다. (구현되지 않음)", "tesseract-psm-3": "OSD 없이 완전히 자동으로 페이지를 분할합니다. (기본값)", "tesseract-psm-4": "다양한 크기의 단일 텍스트 열을 가정합니다.", "tesseract-psm-5": "수직으로 정렬된 단일 텍스트 블록을 가정합니다.", "tesseract-psm-6": "단일 텍스트 블록을 가정합니다.", "tesseract-psm-7": "그림을 단일 텍스트 줄로 취급합니다.", "tesseract-psm-8": "그림을 하나의 단어로 취급합니다.", "tesseract-psm-9": "그림을 원 내부의 하나의 단어로 취급합니다.", "tesseract-psm-10": "그림을 단일 문자로 취급합니다.", "tesseract-psm-11": "산발적인 텍스트. 특별한 정렬 없이 텍스트를 가능한 한 많이 찾습니다.", "tesseract-psm-12": "OSD를 이용한 산발적인 텍스트.", "tesseract-psm-13": "가공되지 않은 줄. 그림을 단일 텍스트 줄로 취급하여 테서랙트 고유의 해킹을 우회합니다.", "tesseract-param-error": "'$2'의 값을 가진 '$1' 옵션은 테러색트에서 지원되지 않습니다. 최댓값: $3", "tesseract-no-text-error": "테서랙트 엔진이 이 그림으로부터 아무런 텍스트를 반환하지 못했습니다.", "tesseract-internal-error": "테서랙트 엔진이 내부 오류를 반환했습니다.", "transkribus-language-code": "언어 모델", "transkribus-unauthorized-error": "오류 코드 '$1' :: 요청이 허가되지 않음", "transkribus-default-error": "오류 코드 '$1' :: 요청을 완료할 수 없음, 다시 시도하세요!", "transkribus-empty-response-error": "트랜스크리버스 API로부터 결과를 파싱하지 못했습니다", "transkribus-init-process-error": "트랜스크리버스 프로세스를 초기화하는데 실패했습니다", "transkribus-failed-process-error": "트랜스크리버스 프로세스 실패", "transkribus-no-lang-error": "선택한 언어가 없습니다", "transkribus-multiple-lang-error": "여러 언어는 허용되지 않습니다, 하나의 언어만 지정하세요", "transkribus-browse-public-models": "트랜스크리버스의 모든 공개 언어 모델 둘러보기", "transkribus-request-for-model": "트랜스크리버스에서 OCR 도구 모델 추가 요청하기", "transkribus-options": "트랜스크리버스 옵션", "transkribus-line-label": "줄 감지 모델", "transkribus-line-id-none-option": "없음", "transkribus-mixed-line-option": "혼합된 줄 방향", "transkribus-line-help": "무슨 줄 감지 모델을 사용해야 하는지 모르겠다면 이 부분을 비워두세요", "transkribus-jobs": "트랜스크리버스 업무", "transkribus-job-id": "업무 ID", "transkribus-job-state": "상태", "transkribus-job-description": "설명", "transkribus-job-start": "시작됨", "transkribus-job-end": "완료", "transkribus-job-waited": "시작 지연 (분)"}