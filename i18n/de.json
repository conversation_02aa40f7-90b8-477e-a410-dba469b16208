{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "Justman10000", "<PERSON>", "TomatoCake"]}, "title": "WikimediaOCR", "subtitle": "Text aus Bildern transkribieren", "form-heading": "Ein Bild transkribieren", "image-url": "Bild-URL", "image-url-help": "Füge eine Bild-U<PERSON> ein, die auf einem Wikimedia-Server gehostet wird, wie z. B.: $1", "image-url-error": "Die Bild-URL muss mit {{PLURAL:$1|dem folgende Domainnamen|einer der folgenden Domainnamen}} beginnen und mit einer gültigen Dateierweiterung enden: $2", "image-alt-text": "Das Originalbild", "language-code": "<PERSON><PERSON><PERSON> (optional)", "engine": "OCR-Modul", "engine-name-transkribus": "Transkribus OCR", "engine-not-found-warning": "Die angeforderte Engine „$1“ wurde nicht gefunden. Verwende stattdessen die Standard-Engine „$2“.", "engine-invalid-langs-warning": "Die folgenden Sprachen sind ungültig oder werden von der Engine nicht unterstützt und wurden ignoriert: $1", "submit": "Ganze Seite transkribieren", "submit-crop": "<PERSON><PERSON>ich transkribieren", "drag-help": "Wähle das Zuschneidewerkzeug und ziehe ein Rechteck auf dem Bild unten, um nur einen Bereich der Seite zu transkribieren.", "drag-mode-move": "Durch Ziehen wird das Bild verschoben", "drag-mode-move-alt": "Symbol für die Aktion „Bewegen“.", "drag-mode-crop": "Durch Z<PERSON> wird ein neuer Zuschnittsbereich erstellt", "drag-mode-crop-alt": "Symbol für die Aktion „Beschneiden“.", "copy-to-clipboard": "In die Zwischenablage kopieren", "copied-to-clipboard": "Kopiert!", "google-error": "Der Google-Dienst gab einen Fehler zurück: $1", "image-retrieval-failed": "Bildabruf fehlgeschlagen: $1", "documentation": "Dokumentation", "api-tooltip": "Die API-Dokumentation anzeigen", "version": "Version $1", "report-issue": "Ein Problem melden", "langs-placeholder": "Für die automatische Spracherkennung leer lassen.", "langs-param-error": "Die {{PLURAL:$1|folgende Sprache wird|folgenden Sprachen werden}} von der OCR-Engine nicht unterstützt: $2", "loading-message": "Transkription wird durchgeführt...", "tesseract-options": "Tesseract-Optionen", "tesseract-psm-label": "Verfahren zur Seitensegmentierung", "tesseract-psm-help": "Versuche „Sparse text“ für eine bessere Erkennung mehrspaltiger Texte.", "tesseract-psm-0": "Nur Orientierungs- und Schrifterkennung (OSD).", "tesseract-psm-1": "Automatische Seitensegmentierung mit OSD.", "tesseract-psm-2": "Automatische Seitensegmentierung, aber weder OSD noch OCR. (nicht implementiert)", "tesseract-psm-3": "Vollautomatische Seitensegmentierung, aber keine OSD. (Standard)", "tesseract-psm-4": "Einspaltiger Text in unterschiedlicher Größe.", "tesseract-psm-5": "Es wird ein einzelner einheitlicher Block mit vertikal ausgerichtetem Text angenommen.", "tesseract-psm-6": "Es wird ein einzelner einheitlicher Textblock angenommen.", "tesseract-psm-7": "Bild mit einer einzelnen Textzeile.", "tesseract-psm-8": "Bild mit einem einzelnen Wort.", "tesseract-psm-9": "Bild mit einem einzelnen kreisförmig geschriebenen Wort.", "tesseract-psm-10": "Bild mit einem einzelnen Zeichen.", "tesseract-psm-11": "Kaum Text. Finde so viel Text wie möglich, die Reihenfolge ist dabei unwichtig.", "tesseract-psm-12": "Knapper Text mit OSD.", "tesseract-psm-13": "Rohzeile. Behandelt das Bild wie eine einzelne Textzeile und umgeht damit Tesseract-spezifische Hacks.", "tesseract-param-error": "Die Option „$1“ mit einem Wert von $2 wird von Tesseract nicht unterstützt. Maximaler Wert: $3", "tesseract-no-text-error": "Die Tesseract-Engine hat keinen Text für dieses Bild geliefert.", "tesseract-internal-error": "Die Tesseract-Engine hat einen internen Fehler zurückgegeben.", "transkribus-language-code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transkribus-unauthorized-error": "Fehlercode '$1' :: Die Anfrage ist nicht autorisiert", "transkribus-default-error": "Fehlercode '$1' ::  Anfrage kann nicht abgeschlossen werden, versuche es erneut!", "transkribus-empty-response-error": "Das Ergebnis der Transkribus-API konnte nicht ausgewertet werden", "transkribus-init-process-error": "Transkribus-Prozess konnte nicht initialisiert werden", "transkribus-failed-process-error": "Transkribus-Prozess fehlgeschlagen", "transkribus-no-lang-error": "<PERSON>s wurde keine Sprache ausgewählt", "transkribus-multiple-lang-error": "<PERSON><PERSON>ere Sprachen sind nicht erlaubt, gib eine Sprache an", "transkribus-browse-public-models": "Alle öffentlichen Sprachmodelle für Transkribus durchsuchen", "transkribus-request-for-model": "<PERSON><PERSON><PERSON> eine Anfrage, um ein Modell aus Transkribus zum OCR-Tool hinzuzufügen", "transkribus-options": "Transkribus-Optionen", "transkribus-line-label": "Modell für Zeilenerkennung", "transkribus-line-id-none-option": "<PERSON><PERSON>", "transkribus-mixed-line-option": "Unterschiedlich ausgerichtete Textzeilen (Mixed Text Line Orientation)", "transkribus-line-help": "<PERSON><PERSON>ld leer, wenn <PERSON> nicht sicher sind, welches Modell Sie für die Erkennung der Textzeilen verwenden sollen.", "transkribus-jobs": "Transkribus-Aufträge", "transkribus-job-id": "Auftragsnummer", "transkribus-job-state": "Status", "transkribus-job-description": "Beschreibung", "transkribus-job-start": "Gestartet", "transkribus-job-end": "<PERSON><PERSON>", "transkribus-job-waited": "Startverzögerung (Minuten)"}