{"@metadata": {}, "title": "WikimediaOCR", "subtitle": "A tool to transcribe text from scanned images on Wikimedia Commons, for use on Wikisource and elsewhere.", "form-heading": "Transcribe an image", "image-url": "Image URL", "image-url-help": "Insert an image URL hosted on a Wikimedia server such as: $1", "image-url-error": "Image URL must begin with {{PLURAL:$1|the following domain name|one of the following domain names}} and end with a valid file extension: $2", "image-alt-text": "The original image", "language-code": "Languages (optional)", "engine": "OCR engine", "engine-name-google": "Google Cloud Vision OCR", "engine-name-tesseract": "Tesseract OCR", "engine-name-transkribus": "Transkribus OCR", "engine-not-found-warning": "The requested engine '$1' was not found. Using the default engine '$2' instead.", "engine-invalid-langs-warning": "The following languages are invalid or not supported by the engine and were ignored: $1", "submit": "Transcribe whole page", "submit-crop": "Transcribe area", "drag-help": "Select the crop tool and drag a rectangle on the image below to transcribe only one area of the page.", "drag-mode-move": "Dragging will move the image", "drag-mode-move-alt": "Icon representing the 'move' action.", "drag-mode-crop": "Dragging will create a new crop area", "drag-mode-crop-alt": "Icon representing the 'crop' action.", "copy-to-clipboard": "Copy to clipboard", "copied-to-clipboard": "Copied!", "google-error": "The Google service returned an error: $1", "image-retrieval-failed": "Image retrieval failed: $1", "documentation": "Documentation", "api": "API", "api-tooltip": "View the API documentation", "version": "Version $1", "report-issue": "Report an issue", "langs-placeholder": "Leave blank for automatic language detection.", "langs-param-error": "The following {{PLURAL:$1|language is|languages are}} not supported by the OCR engine: $2", "loading-message": "Performing transcription...", "tesseract-options": "Tesseract options", "tesseract-psm-label": "Page segmentation method", "tesseract-psm-help": "Try \"Sparse text\" for better multi-column support.", "tesseract-psm-0": "Orientation and script detection (OSD) only.", "tesseract-psm-1": "Automatic page segmentation with OSD.", "tesseract-psm-2": "Automatic page segmentation, but no OSD, or OCR. (not implemented)", "tesseract-psm-3": "Fully automatic page segmentation, but no OSD. (<PERSON><PERSON><PERSON>)", "tesseract-psm-4": "Assume a single column of text of variable sizes.", "tesseract-psm-5": "Assume a single uniform block of vertically aligned text.", "tesseract-psm-6": "Assume a single uniform block of text.", "tesseract-psm-7": "Treat the image as a single text line.", "tesseract-psm-8": "Treat the image as a single word.", "tesseract-psm-9": "Treat the image as a single word in a circle.", "tesseract-psm-10": "Treat the image as a single character.", "tesseract-psm-11": "Sparse text. Find as much text as possible in no particular order.", "tesseract-psm-12": "Sparse text with OSD.", "tesseract-psm-13": "Raw line. Treat the image as a single text line, bypassing hacks that are Tesseract-specific.", "tesseract-param-error": "The '$1' option with a value of $2 is not supported by Tesseract. Maximum value: $3", "tesseract-no-text-error": "The Tesseract engine did not return any text for this image.", "tesseract-internal-error": "The tesseract engine returned an internal error.", "transkribus-language-code": "Language Model", "transkribus-unauthorized-error": "Error Code '$1' :: The request is not authorized", "transkribus-default-error": "Error Code '$1' ::  Unable to complete request, try again!", "transkribus-empty-response-error": "Could not parse result from Transkribus API", "transkribus-init-process-error": "Failed to initialize Transkribus process", "transkribus-failed-process-error": "Transkribus process failed", "transkribus-no-lang-error": "No language was selected", "transkribus-multiple-lang-error": "Multiple languages are not allowed, specify one language", "transkribus-browse-public-models": "Browse all public language models for Transkribus", "transkribus-request-for-model": "Make a request to add a model from Transkribus to the OCR tool", "transkribus-options": "Transkribus Options", "transkribus-line-label": "Line Detection Model", "transkribus-line-id-none-option": "None", "transkribus-mixed-line-option": "Mixed Line Orientation", "transkribus-line-help": "Leave empty if you are not sure of which line detection model to use", "transkribus-jobs": "Transkribus Jobs", "transkribus-job-id": "Job ID", "transkribus-job-state": "State", "transkribus-job-description": "Description", "transkribus-job-start": "Started", "transkribus-job-end": "Finished", "transkribus-job-waited": "Start delay (minutes)"}