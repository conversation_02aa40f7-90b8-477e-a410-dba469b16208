{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Slickdaddy"]}, "title": "WikimediaOCR", "subtitle": "Wikimedia Commons'ta taranmış görsellerden metinleri yazıya dökmek ve Vikikaynak ve diğer yerlerde kullanmak için bir araç.", "form-heading": "<PERSON><PERSON> görü<PERSON>ü<PERSON>ü metne dö<PERSON>", "image-url": "Resim URL'si", "image-url-help": "Bir Wikimedia sunucusunda barındırılan bir resim URL'si ekleyin, örneğin: $1", "image-url-error": "Resim URL'si {{PLURAL:$1|şu alan adıyla|şu alan adlarından biriyle}} başlamalı ve geçerli bir dosya uzantısıyla bitmelidir: $2", "image-alt-text": "Özgün resim", "language-code": "<PERSON><PERSON> (isteğe bağlı)", "engine": "OCR motoru", "engine-name-transkribus": "Transkribus OCR", "engine-not-found-warning": "İstenen motor ' $1 ' bulunamadı. Bunun yerine var<PERSON> motor ' $2 ' kullanılıyor.", "engine-invalid-langs-warning": "Aşağıdak<PERSON> diller geçersizdir veya altyapı tarafından desteklenmez ve yok sayılır: $1", "submit": "Tüm sayfayı transkript et", "submit-crop": "Transkript alanı", "drag-help": "Sayfanın yalnızca bir alanını kopyalamak için kırpma aracını seçin ve aşağıdaki resimde bir dikdörtgeni sürükleyin.", "drag-mode-move": "Sürüklemek görüntüyü hareket ettirir", "drag-mode-move-alt": "'<PERSON><PERSON><PERSON>' eylemini temsil eden simge.", "drag-mode-crop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, yeni bir kırpma alanı oluşturacak", "drag-mode-crop-alt": "'<PERSON><PERSON><PERSON><PERSON>' e<PERSON><PERSON> temsil eden simge.", "copy-to-clipboard": "<PERSON><PERSON> k<PERSON>", "copied-to-clipboard": "Kopyalandı!", "google-error": "Google hizmeti bir hata verdi: $1", "image-retrieval-failed": "Resim alınamadı: $1", "documentation": "Belgelendirme", "api": "API", "api-tooltip": "API belgelerini görüntüleyin", "version": "Sürüm $1", "report-issue": "<PERSON><PERSON>", "langs-placeholder": "Otomatik dil algılama için boş bırakın.", "langs-param-error": "Şu {{PLURAL:$1|dil|diller}}, OCR motoru tarafından desteklenmemektedir: $2", "loading-message": "Transkripsiyon gerçekleştiriliyor...", "tesseract-options": "<PERSON><PERSON><PERSON>", "tesseract-psm-label": "<PERSON><PERSON><PERSON><PERSON>", "tesseract-psm-help": "Daha iyi çoklu sütun desteği için \"Aralıklı metin\"i deneyin.", "tesseract-psm-0": "Yalnızca yönlendirme ve komut dosyası algılama (OSD).", "tesseract-psm-1": "OSD ile otomatik sayfa bölümleme.", "tesseract-psm-2": "Otomatik sayfa bölümleme ancak OSD veya OCR mevcut değil. (uygulanmadı)", "tesseract-psm-3": "Tam otomatik sayfa bölümleme ancak OSD mevcut değil. (Varsayılan)", "tesseract-psm-4": "Değişken boyutlarda tek bir metin sütunu var<PERSON>ın.", "tesseract-psm-5": "Dikey olarak hizalanmış tek bir tek tip metin bloğu var<PERSON>ın.", "tesseract-psm-6": "Tek bir tek tip metin blo<PERSON>n.", "tesseract-psm-7": "<PERSON><PERSON><PERSON>, tek bir metin satırı olarak ele alın.", "tesseract-psm-8": "<PERSON><PERSON><PERSON>, tek bir kelime olarak ele alın.", "tesseract-psm-9": "Resmi bir daire içinde tek bir kelime olarak ele alın.", "tesseract-psm-10": "<PERSON><PERSON><PERSON>, tek bir karakter olarak ele alın.", "tesseract-psm-11": "Aralıklı metin. Bel<PERSON>li bir sırayla mümkün olduğunca fazla metin bulun.", "tesseract-psm-12": "OSD ile aralıklı metin.", "tesseract-psm-13": "Ham satır. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tesseract'a özgü saldırıları atlayarak tek bir metin satırı olarak ele alın.", "tesseract-param-error": "$2 değerine sahip '$1' seçeneği Tesseract tarafından desteklenmez. Maksimum değer: $3", "tesseract-no-text-error": "Tesseract motoru bu g<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>in herhangi bir metin döndü<PERSON>.", "tesseract-internal-error": "Tesseract motoru dahili bir hata verdi.", "transkribus-language-code": "<PERSON><PERSON> Modeli", "transkribus-unauthorized-error": "Hata Kodu '$1' :: İstek yetkilendirilmedi", "transkribus-default-error": "Hata Kodu '$1' :: <PERSON><PERSON>k <PERSON>ı, tekra<PERSON> den<PERSON>!", "transkribus-empty-response-error": "Transkribus API'sinden sonuç ayrıştırılamadı", "transkribus-init-process-error": "Transkribus işlemi başlatılamadı", "transkribus-failed-process-error": "Transkribus işlemi başarısız oldu", "transkribus-no-lang-error": "<PERSON><PERSON><PERSON><PERSON>", "transkribus-multiple-lang-error": "<PERSON>en fazla dile izin verilmiyor, bir dil belirtin", "transkribus-browse-public-models": "Transkribus için tüm genel dil modellerine göz atın", "transkribus-request-for-model": "Transkribus'tan OCR aracına bir model eklemek için istekte bulunun", "transkribus-options": "Transkribus Seçenekleri", "transkribus-line-label": "Çizgi Algılama Modeli", "transkribus-line-id-none-option": "Hiç<PERSON>i", "transkribus-mixed-line-option": "Karışık Çizgi Yönü", "transkribus-line-help": "Hangi hat algılama modelini kullanacağınızdan emin de<PERSON> boş bırakın", "transkribus-jobs": "Transkribus İşleri", "transkribus-job-id": "İş Kimliği", "transkribus-job-state": "Durum", "transkribus-job-description": "<PERSON><PERSON>ı<PERSON><PERSON>", "transkribus-job-start": "Başladı", "transkribus-job-end": "<PERSON><PERSON>", "transkribus-job-waited": "Başlat<PERSON> (dakika)"}