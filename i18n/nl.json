{"@metadata": {"authors": ["HanV", "McDut<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, "title": "WikimediaOCR", "subtitle": "<PERSON><PERSON> hulp<PERSON> om tekst uit gescande afbeeldingen op Wikimedia Commons over te schrijven, voor gebruik op Wikisource en elders.", "form-heading": "Transcribeer e<PERSON> a<PERSON><PERSON><PERSON>", "image-url": "Afbeeldings-URL", "image-url-help": "Voeg een afbeeldings-URL in die wordt gehost op een Wikimedia-server, zoals: $1", "image-url-error": "Afbeeldings-URL moet beginnen met {{PLURAL:$1| de volgende domeinnaam|een van de volgende domeinnamen}} en eindigen met een geldige bestandsextensie: $2", "image-alt-text": "De originele afbeelding", "language-code": "<PERSON><PERSON> (optioneel)", "engine": "OCR-engine", "engine-name-google": "Google Cloud Vision OCR", "engine-name-tesseract": "Tesseract OCR", "engine-name-transkribus": "Transkribus-OCR", "engine-not-found-warning": "De gevraagde engine '$1' werd niet gevonden. Gebruik in plaats daarvan de standaardengine '$2'.", "engine-invalid-langs-warning": "De volgende talen zijn ongeldig of worden niet ondersteund door de engine en werden genegeerd: $1", "submit": "Transcribeer de hele pagina", "submit-crop": "Transcribeer e<PERSON> g<PERSON><PERSON>", "drag-help": "Selecteer het gereedschap bijsnijden en selecteer een recht<PERSON> op de onderstaande afbeelding om slechts é<PERSON> gebied van de pagina te transcriberen.", "drag-mode-move": "Door te slepen wordt de afbeelding verplaatst", "drag-mode-move-alt": "Pictogram dat de handeling ‘verplaatsen’ aanduidt.", "drag-mode-crop": "Door te slepen wordt een nieuw bijsnijdgebied gemaakt", "drag-mode-crop-alt": "Pictogram dat de handeling ‘bijsnijden’ aanduidt.", "copy-to-clipboard": "<PERSON>ar klembord kopiëren", "copied-to-clipboard": "Gekopieerd!", "google-error": "De Google-service heeft een fout geretourneerd: $1", "image-retrieval-failed": "<PERSON><PERSON><PERSON> van afbeelding mislukt: $1", "documentation": "Documentatie", "api-tooltip": "Bekijk de API-documentatie", "version": "Versie $1", "report-issue": "Pro<PERSON>em melden", "langs-placeholder": "Laat leeg voor automatische taaldetectie.", "langs-param-error": "De volgende {{PLURAL:$1|taal wordt|talen worden}} niet ondersteund door de OCR-engine: $2", "loading-message": "Transcriptie uitvoeren...", "tesseract-options": "Tesseract-opties", "tesseract-psm-label": "Pagina-segmentatiemethode", "tesseract-psm-help": "<PERSON><PERSON><PERSON> \"Scha<PERSON>e tekst\" voor een betere ondersteuning van meerdere kolommen.", "tesseract-psm-0": "Alleen oriëntatie en scriptdetectie (OSD).", "tesseract-psm-1": "Automatische paginasegmentatie met OSD.", "tesseract-psm-2": "Automatische paginasegmentatie, maar geen OSD of OCR (niet geïmplementeerd).", "tesseract-psm-3": "Volautomatische paginasegmentatie, maar geen OSD (standaard).", "tesseract-psm-4": "Ga uit van een enkele tekstkolom van variabele grootte.", "tesseract-psm-5": "Ga uit van een enkel uniform blok verticaal uitgelijnde tekst.", "tesseract-psm-6": "Ga uit van één uniform blok tekst.", "tesseract-psm-7": "Be<PERSON>el de afbeelding als een enkele tekstregel.", "tesseract-psm-8": "<PERSON><PERSON><PERSON> de afbeelding als een enkel woord.", "tesseract-psm-9": "Behandel de afbeelding als een enkel woord in een cirkel.", "tesseract-psm-10": "<PERSON><PERSON><PERSON> de afbeelding als een enkel teken.", "tesseract-psm-11": "Schaarse tekst. Zoek zoveel mogelijk tekst in willekeurige volgorde.", "tesseract-psm-12": "Spaarzame tekst met OSD.", "tesseract-psm-13": "<PERSON><PERSON><PERSON> lijn. Behandel de afbeelding als een enkele tekstregel en omzeil hacks die Tesseract-specifiek zijn.", "tesseract-param-error": "De optie '$1' met een waarde $2 wordt niet ondersteund door Tesseract. Maximale waarde: $3", "tesseract-no-text-error": "De Tesseract-engine heeft geen tekst voor deze afbeelding geretourneerd.", "tesseract-internal-error": "De tesseract-engine heeft een interne fout geretourneerd.", "transkribus-language-code": "Taalmodel", "transkribus-unauthorized-error": "Foutcode '$1' :: He<PERSON> verz<PERSON><PERSON> is niet geautoriseerd", "transkribus-default-error": "Foutcode '$1' :: Kan het verzoek niet volto<PERSON>, probeer het opnieuw!", "transkribus-empty-response-error": "Kon het resultaat van de API van Transkribus niet interpreteren", "transkribus-init-process-error": "Kon het Transkribus-proces niet initialiseren", "transkribus-failed-process-error": "Transkribus-proces mislukt", "transkribus-no-lang-error": "Er is geen taal geselecteerd", "transkribus-multiple-lang-error": "<PERSON><PERSON><PERSON> talen zijn niet <PERSON>, gee<PERSON> op", "transkribus-browse-public-models": "Door alle openbare taalmodellen voor Transkribus bladeren", "transkribus-request-for-model": "Verzoeken om een model uit Transkribus toe te voegen aan het OCR-programma", "transkribus-options": "Transkribus-opties", "transkribus-line-label": "Lijndetectiemodel", "transkribus-line-id-none-option": "<PERSON><PERSON>", "transkribus-mixed-line-option": "Gemengde lijnoriëntatie", "transkribus-line-help": "Laat dit leeg als u niet zeker weet welk lijndetectiemodel u moet gebruiken", "transkribus-jobs": "Transkribus-taken", "transkribus-job-id": "Taak-ID", "transkribus-job-state": "Status", "transkribus-job-description": "Beschrijving", "transkribus-job-start": "Gestart", "transkribus-job-end": "Voltooid", "transkribus-job-waited": "Startvertraging (minuten)"}