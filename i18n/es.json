{"@metadata": {"authors": ["Agent", "<PERSON><PERSON><PERSON>", "Atzerritik", "Avengium", "Dmaza", "<PERSON><PERSON><PERSON>", "Jackiezelaya", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Ninovolador", "Superzerocool"]}, "title": "WikimediaOCR", "subtitle": "Transcribe texto de la imagen", "form-heading": "Transcribe una imagen", "image-url": "En<PERSON> de la imagen:", "image-url-help": "Inserte un enlace a una imagen alojada en un servidor de Wikimedia como: $1", "image-url-error": "Los LRUs (URLs) de imagenes deben empezar con {{PLURAL:$1|el siguiente dominio de internet|uno de los siguientes dominios de internet}} y deben terminar en una extensión de archivo válida: $2", "image-alt-text": "La imagen original", "language-code": "Código de idioma de dos letras (opcional):", "engine": "Motor de OCR:", "engine-name-transkribus": "OCR de Transkribus", "engine-not-found-warning": "No se encontró el motor solicitado '$1'. Se utiliza el motor predeterminado '$2'.", "engine-invalid-langs-warning": "Los siguientes idiomas son no válidos o no están soportados por el motor y se han ignorado: $1", "submit": "Transcribir toda la página", "submit-crop": "Transcribir área", "drag-help": "Selecciona la herramienta de recorte y arrastra un rectángulo en la imagen de abajo para transcribir solo un área de la página.", "drag-mode-move": "Arrastrar mover<PERSON> la <PERSON>n", "drag-mode-move-alt": "Icono que representa la acción 'mover'.", "drag-mode-crop": "Arrastrar creará una nueva área de recorte", "drag-mode-crop-alt": "Icono que representa la acción 'recortar'.", "copy-to-clipboard": "Copiar en el portapapeles", "copied-to-clipboard": "¡Copiado!", "google-error": "El servicio de Google regresó un error: $1", "image-retrieval-failed": "Error al recuperar la imagen: $1", "documentation": "Documentación", "api-tooltip": "Ver la documentación de la API", "version": "Versión $1", "report-issue": "Reportar un problema", "langs-placeholder": "Dejar en blanco para usar detección automática de idioma.", "langs-param-error": "{{PLURAL:$1|El siguiente idioma no es compatible|Los siguientes idiomas no son compatibles}} con el motor de OCR: $2", "loading-message": "Realizando transcripción...", "tesseract-options": "Opciones de Tesseract:", "tesseract-psm-label": "Método de segmentación de páginas", "tesseract-psm-help": "<PERSON><PERSON><PERSON> \"Texto escaso\" para una mejor compatibilidad con varias columnas.", "tesseract-psm-0": "Sólo detección de orientación y sistema de escritura (OSD).", "tesseract-psm-1": "Segmentación automática de páginas con OSD.", "tesseract-psm-2": "Segmentación automática de páginas, pero sin OSD ni OCR. (no implementado)", "tesseract-psm-3": "Segmentación de páginas totalmente automática, pero sin OSD. (Valor predeterminado)", "tesseract-psm-4": "As<PERSON>r una sola columna de texto de tamaños variables.", "tesseract-psm-5": "<PERSON><PERSON>r un solo bloque uniforme de texto alineado verticalmente.", "tesseract-psm-6": "<PERSON><PERSON>r un solo bloque uniforme de texto.", "tesseract-psm-7": "Tratar la imagen como una sola línea de texto.", "tesseract-psm-8": "Tratar la imagen como una sola palabra.", "tesseract-psm-9": "Tratar la imagen como una sola palabra en un círculo.", "tesseract-psm-10": "Tratar la imagen como un solo carácter.", "tesseract-psm-11": "Texto escaso. Busque la mayor cantidad de texto posible sin ningún orden en particular.", "tesseract-psm-12": "Texto escaso con OSD.", "tesseract-psm-13": "<PERSON><PERSON><PERSON> crud<PERSON>. Tratar la imagen como una sola línea de texto, ignorando los hacks que son específicos de Tesseract.", "tesseract-param-error": "Tesseract no admite la opción \"$1\" con un valor de $2. <PERSON>or máximo: $3", "tesseract-no-text-error": "El motor Tesseract no devolvió ningún texto para esta imagen.", "tesseract-internal-error": "El motor Tesseract regresó un error interno.", "transkribus-language-code": "<PERSON><PERSON>ng<PERSON>", "transkribus-unauthorized-error": "Código de error '$1' :: La solicitud no está autorizada", "transkribus-default-error": "Código de error '$1' :: No se puede completar la solicitud, ¡inténtelo de nuevo!", "transkribus-empty-response-error": "No se pudo analizar el resultado de la API de Transkribus", "transkribus-init-process-error": "No se pudo inicializar el proceso Transkribus", "transkribus-failed-process-error": "El proceso de Transkribus falló", "transkribus-no-lang-error": "No se seleccionó ningún idioma", "transkribus-multiple-lang-error": "No se permiten múltiples idiomas, especifique uno", "transkribus-browse-public-models": "Explorar todos los modelos de idioma públicos para Transkribus", "transkribus-request-for-model": "Realizar una solicitud para agregar un modelo de Transkribus a la herramienta OCR", "transkribus-options": "Opciones de Transkribus", "transkribus-line-label": "Modelo de detección de líneas", "transkribus-line-id-none-option": "<PERSON><PERSON><PERSON>", "transkribus-mixed-line-option": "Orientación de línea mixta", "transkribus-line-help": "Deje vacío si no está seguro de qué modelo de detección de líneas utilizar", "transkribus-jobs": "Tareas de Transkribus", "transkribus-job-id": "<PERSON>d<PERSON> de la tarea", "transkribus-job-state": "Estado", "transkribus-job-description": "Descripción", "transkribus-job-start": "Iniciado", "transkribus-job-end": "Finalizado", "transkribus-job-waited": "<PERSON><PERSON><PERSON> de inicio (minutos)"}