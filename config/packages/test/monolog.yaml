monolog:
    channels: ['main', 'nested', 'tesseract']
    handlers:
        main:
            type: fingers_crossed
            action_level: error
            handler: nested
            excluded_http_codes: [404, 405]
            channels: ["!event"]
        nested:
            type: stream
            path: "%kernel.logs_dir%/%kernel.environment%.log"
            level: debug
        tesseract:
            type: fingers_crossed
            action_level: error
            handler: nested
