monolog:
    channels: ['main', 'tesseract']
    handlers:
        main:
            type: fingers_crossed
            action_level: error
            handler: nested
            excluded_http_codes: [404, 405]
            buffer_size: 50 # How many messages should be saved? Prevent memory leaks
        nested:
            type: stream
            path: php://stderr
            level: debug
            formatter: monolog.formatter.json
        console:
            type: console
            process_psr_3_messages: false
            channels: ["!event", "!doctrine"]
        mailer:
            type: deduplication
            time: 300
            handler: symfony_mailer
        symfony_mailer:
            type: symfony_mailer
            level: critical
            from_email: '%env(APP_MAIL_SENDER)%'
            to_email:
                - '%env(APP_LOG_RECIPIENT_1)%'
                - '%env(APP_LOG_RECIPIENT_2)%'
            subject: '%env(APP_LOG_SUBJECT)% %%message%%'
            formatter: monolog.formatter.html
            content_type: text/html
        tesseract:
            type: fingers_crossed
            action_level: error
            handler: nested
