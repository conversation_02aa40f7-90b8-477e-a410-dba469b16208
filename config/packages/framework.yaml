# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'
    #csrf_protection: true
    #http_method_override: true

    # Enables session support. Note that the session will ONLY be started if you read or write from it.
    # Remove or comment this section to explicitly disable session support.
    session:
        storage_factory_id: "session.storage.factory.native"
        cookie_secure: auto
        cookie_samesite: lax

    #esi: true
    #fragments: true
    php_errors:
        log: true

    http_client:
        default_options:
            retry_failed:
                max_retries: 3
                delay: 1000
                multiplier: 3
                max_delay: 5000
                jitter: 0.3
                http_codes:
                    0: ['GET', 'HEAD']
                    423: true
                    425: true
                    429: true
                    502: true
                    503: true
                    500: [ 'GET', 'HEAD' ]
                    504: [ 'GET', 'HEAD' ]
                    507: [ 'GET', 'HEAD' ]
                    510: [ 'GET', 'HEAD' ]
