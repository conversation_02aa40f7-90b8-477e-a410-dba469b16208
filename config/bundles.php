<?php
declare( strict_types = 1 );

return [
	Symfony\Bundle\FrameworkBundle\FrameworkBundle::class => [ 'all' => true ],
	Doctrine\Bundle\DoctrineBundle\DoctrineBundle::class => [ 'all' => true ],
	Wikimedia\ToolforgeBundle\ToolforgeBundle::class => [ 'all' => true ],
	Symfony\Bundle\TwigBundle\TwigBundle::class => [ 'all' => true ],
	Twig\Extra\TwigExtraBundle\TwigExtraBundle::class => [ 'all' => true ],
	Symfony\Bundle\WebProfilerBundle\WebProfilerBundle::class => [ 'dev' => true, 'test' => true ],
	Symfony\WebpackEncoreBundle\WebpackEncoreBundle::class => [ 'all' => true ],
	Symfony\Bundle\MonologBundle\MonologBundle::class => [ 'all' => true ],
	Nelmio\ApiDocBundle\NelmioApiDocBundle::class => [ 'all' => true ],
];
