<?xml version="1.0" ?>
<ruleset name="wikimedia-ocr">
    <rule ref="./vendor/mediawiki/mediawiki-codesniffer/MediaWiki">
        <properties>
            <property name="rootNamespaces" type="array">
                <element key="src" value="App" />
                <element key="tests" value="App\Tests" />
            </property>
        </properties>
    </rule>
	<file>.</file>
	<arg name="bootstrap" value="./vendor/mediawiki/mediawiki-codesniffer/utils/bootstrap-ci.php"/>
	<arg name="extensions" value="php"/>
	<arg name="encoding" value="UTF-8"/>

    <file>.</file>
    <exclude-pattern>./vendor/</exclude-pattern>
    <exclude-pattern>./var/</exclude-pattern>
    <exclude-pattern>src/Kernel.php</exclude-pattern>
    <exclude-pattern>./node_modules/</exclude-pattern>
    <exclude-pattern>./bin/.phpunit/</exclude-pattern>
    <exclude-pattern>./public/build/</exclude-pattern>
    <exclude-pattern>./public/bundles/</exclude-pattern>
    <exclude-pattern>./assets/</exclude-pattern>
</ruleset>
