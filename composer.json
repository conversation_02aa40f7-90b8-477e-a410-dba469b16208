{"name": "wikimedia/wikimedia-ocr", "description": "A simple wrapper around multiple OCR engines, enabling Wikisources to submit images for OCR and retrieve the resultant text.", "type": "project", "license": "GPL-3.0-or-later", "require": {"php": ">=7.3", "ext-bcmath": "*", "ext-ctype": "*", "ext-gd": "*", "ext-iconv": "*", "ext-json": "*", "google/cloud-vision": "^1.3", "imagine/imagine": "^1.2", "nelmio/api-doc-bundle": "^4.4", "predis/predis": "^2.2", "symfony/cache": "5.2.*", "symfony/console": "5.2.*", "symfony/dotenv": "5.2.*", "symfony/framework-bundle": "^5.4", "symfony/mailer": "^5.2", "symfony/monolog-bundle": "^3.7", "symfony/property-info": "5.2.*", "symfony/twig-bundle": "5.2.*", "symfony/webpack-encore-bundle": "^1.11", "symfony/yaml": "5.2.*", "thiagoalessio/tesseract_ocr": "^2.11", "twig/extra-bundle": "^2.12|^3.0", "twig/intl-extra": "^3.7", "twig/twig": "^2.12|^3.0", "wikimedia/toolforge-bundle": "^1.3"}, "require-dev": {"drenso/phan-extensions": "^3.3", "mediawiki/mediawiki-codesniffer": "^39.0", "mediawiki/minus-x": "^1.1", "mediawiki/phan-taint-check-plugin": "^4.0", "symfony/phpunit-bridge": "^5.2", "symfony/stopwatch": "^5.2", "symfony/web-profiler-bundle": "^5.2"}, "config": {"platform": {"php": "7.3.31"}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*"}, "scripts": {"auto-scripts": ["./bin/console cache:clear", "./bin/console assets:install"], "check-tesseract": "./check_tesseract.sh", "post-install-cmd": ["@auto-scripts", "@check-tesseract"], "post-update-cmd": ["@auto-scripts", "@check-tesseract"], "test": ["@test-common", "@phan"], "test-common": ["composer validate", "phpcs -s -p .", "./bin/console lint:twig ./templates", "./bin/console lint:yaml ./config", "minus-x check .", "@check-tesseract", "./bin/phpunit"], "phan": ["phan --allow-polyfill-parser --long-progress-bar"], "fix": "phpcbf"}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "5.2.*"}}}