{"devDependencies": {"@symfony/webpack-encore": "^1.8.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "bootstrap": "3.4.1", "core-js": "^3.32.1", "cropperjs": "^1.6.0", "file-loader": "^6.2.0", "grunt": "^1.6.1", "grunt-banana-checker": "^0.11.0", "grunt-stylelint": "^0.19.0", "jquery": "^3.7.1", "regenerator-runtime": "^0.13.11", "select2": "^4.0.13", "select2-bootstrap-theme": "0.1.0-beta.10", "stylelint-config-wikimedia": "^0.15.0", "webpack-notifier": "^1.15.0"}, "license": "GPL-3.0-or-later", "private": true, "scripts": {"dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress", "test": "grunt test"}}